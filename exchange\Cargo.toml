[package]
name = "exchange"
version = "0.1.0"
edition = "2024"

[dependencies]
iced_futures.workspace = true
chrono.workspace = true
serde.workspace = true
serde_json.workspace = true
log.workspace = true
rust_decimal.workspace = true
thiserror.workspace = true
ordered-float.workspace = true
uuid.workspace = true
enum-map.workspace = true
rustc-hash.workspace = true

tokio = { version = "1.43", default-features = false, features = ["rt", "macros"] }
reqwest = { version = "0.12.9", default-features = false, features = ["json", "brotli", "rustls-tls"] }
bytes = "1.8.0"
sonic-rs = { version = "0.5.0", default-features = false }
fastwebsockets = { version = "0.9.0", default-features = false, features = ["upgrade"] }
http-body-util = "0.1.2"
hyper = { version = "1", default-features = false, features = ["http1", "client"] }
hyper-util = { version = "0.1.10", default-features = false }
tokio-rustls = "0.24.1"
webpki-roots = "0.23.1"
zip = "2.3.0"
csv = "1.3.1"