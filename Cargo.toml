[package]
name = "flowsurface"
version = "0.8.0"
edition = "2024"
readme = "README.md"
license = "GPL-3.0-or-later"
categories = ["finance", "visualization", "gui"]
repository = "https://github.com/akenshaw/flowsurface"

[workspace]
members = [ "data", "exchange"]

[workspace.dependencies]
iced_futures = { version = "0.14.0-dev" }
iced_core = { version = "0.14.0-dev", features = ["serde"] }
chrono = { version = "0.4.40", default-features = false, features = ["serde", "now", "clock"] }
serde = { version = "1.0.219", default-features = false, features = ["derive"] }
serde_json = "1.0.140"
log = "0.4.22"
thiserror = "2.0.12"
ordered-float = "5.0.0"
regex = "1.11.1"
rust_decimal = "1.36.0"
palette = "0.7.6"
num-traits = "0.2.19"
rustc-hash = "2.1.1"
enum-map = "2.7.3"
uuid = { version = "1.11.0", features = ["v4"] }

[dependencies]
iced = { version = "0.14.0-dev", default-features = false, features = [
    "wgpu",
    "tokio",
    "canvas",
    "sipper",
    "advanced",
    "unconditional-rendering",
] }
iced_core.workspace = true
iced_futures.workspace = true
chrono.workspace = true
serde_json.workspace = true
serde.workspace = true
thiserror.workspace = true
ordered-float.workspace = true
log.workspace = true
uuid.workspace = true
palette.workspace = true
num-traits.workspace = true
rustc-hash.workspace = true
enum-map.workspace = true
fern = "0.7.1"

exchange = { version = "0.1.0", path = "exchange" }
data = { version = "0.1.0", path = "data" }

[features]
debug = ["iced/hot"]

[patch.crates-io]
iced = { git = "https://github.com/iced-rs/iced", rev = "04639a419458dcb421534461c69d5f953bf829b2" }
iced_futures = { git = "https://github.com/iced-rs/iced", rev = "04639a419458dcb421534461c69d5f953bf829b2" }
iced_core = { git = "https://github.com/iced-rs/iced", rev = "04639a419458dcb421534461c69d5f953bf829b2"}
