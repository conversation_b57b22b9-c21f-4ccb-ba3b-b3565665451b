pub mod ticks;
pub mod time;

use serde::{Deserialize, Serialize};

#[derive(<PERSON>bu<PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>q, Eq, Serialize, Deserialize)]
pub struct TickCount(pub u16);

impl TickCount {
    pub const ALL: [TickCount; 7] = [
        Tick<PERSON><PERSON>nt(10),
        Tick<PERSON><PERSON>nt(20),
        <PERSON><PERSON><PERSON><PERSON>nt(50),
        Tick<PERSON><PERSON>nt(100),
        Tick<PERSON>ount(200),
        Tick<PERSON>ount(500),
        Tick<PERSON>ount(1000),
    ];

    pub fn is_custom(&self) -> bool {
        !Self::ALL.contains(self)
    }
}

impl std::fmt::Display for TickCount {
    fn fmt(&self, f: &mut std::fmt::Formatter) -> std::fmt::Result {
        write!(f, "{}T", self.0)
    }
}
