name: Clippy

on:
    push:
        branches: [main]
    pull_request:
        branches: [main]
    workflow_dispatch:

jobs:
    clippy:
        if: ${{ github.actor != 'dependabot[bot]' && github.actor != 'dependabot-preview[bot]' }}
        runs-on: ubuntu-latest

        steps:
            - uses: actions/checkout@v4

            - name: Install Linux system dependencies
              if: runner.os == 'Linux'
              run: |
                  sudo apt-get update
                  sudo apt-get install -y build-essential pkg-config libasound2-dev

            - name: Install rust toolchain + clippy
              uses: actions-rs/toolchain@v1
              with:
                  toolchain: stable
                  components: clippy
                  override: true

            - name: Cache cargo registry & target
              uses: actions/cache@v4
              with:
                  path: |
                      ~/.cargo/registry
                      ~/.cargo/git
                      target
                  key: ${{ runner.os }}-cargo-${{ hashFiles('**/Cargo.lock') }}
                  restore-keys: |
                      ${{ runner.os }}-cargo-

            - name: Fetch dependencies
              run: cargo fetch

            - name: Run cargo clippy
              run: cargo clippy --workspace --all-targets --all-features -- -D warnings
