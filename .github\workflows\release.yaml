name: "Release"

on:
    workflow_dispatch:
        inputs:
            tag:
                description: "Specify tag to create"
                required: true

jobs:
    build:
        name: Build
        strategy:
            matrix:
                target:
                    - target: macos
                      os: macos-latest
                      make: bash scripts/build-macos.sh
                      artifact_path: |
                          echo "ARTIFACT_PATH=target/release/flowsurface-universal-macos.tar.gz" >> "$GITHUB_ENV"
                    - target: windows
                      os: windows-latest
                      make: bash scripts/build-windows.sh
                      artifact_path: |
                          echo "ARTIFACT_PATH=target/release/flowsurface-x86_64-windows.zip" >> $env:GITHUB_ENV
                    - target: linux
                      os: ubuntu-latest
                      make: bash scripts/package-linux.sh package
                      artifact_path: |
                          echo "ARTIFACT_PATH=$(bash scripts/package-linux.sh archive_path)" >> "$GITHUB_ENV"
        runs-on: ${{ matrix.target.os }}
        steps:
            - uses: actions/checkout@v3
            - uses: actions-rs/toolchain@v1
              with:
                  profile: minimal
                  toolchain: stable
                  override: true

            - name: Install linux deps
              if: matrix.target.target == 'linux'
              run: |
                  sudo apt update
                  sudo apt install -y \
                    build-essential \
                    git \
                    pkg-config \
                    libudev-dev \
                    libxkbcommon-dev \
                    libasound2-dev

            - name: Configure optimized release profile
              shell: bash
              run: |
                  if grep -q "\[profile.release\]" Cargo.toml; then
                    sed -i '/\[profile.release\]/,/^\[/s/^lto.*//g' Cargo.toml
                    sed -i '/\[profile.release\]/,/^\[/s/^codegen-units.*//g' Cargo.toml
                    sed -i '/\[profile.release\]/,/^\[/s/^opt-level.*//g' Cargo.toml
                    
                    sed -i '/\[profile.release\]/a opt-level = 3\ncodegen-units = 1\nlto = "fat"' Cargo.toml
                  else
                    cat >> Cargo.toml << EOF

                  [profile.release]
                  lto = "fat"
                  codegen-units = 1
                  opt-level = 3
                  EOF
                  fi

                  rm -f .cargo/config.toml

            - name: Build
              run: ${{ matrix.target.make }}

            - name: Set artifact path
              run: ${{ matrix.target.artifact_path }}

            - name: Upload artifact
              uses: actions/upload-artifact@v4
              env:
                  ARTIFACT_PATH: ${{ env.ARTIFACT_PATH }}
              with:
                  name: ${{ matrix.target.target }}
                  path: ${{ env.ARTIFACT_PATH }}

    create-release:
        needs: build
        name: Create Release
        outputs:
            upload_url: ${{ steps.create-release.outputs.upload_url }}
        runs-on: ubuntu-latest
        env:
            GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}

        steps:
            - name: Create Release
              id: create-release
              uses: actions/create-release@v1
              with:
                  tag_name: ${{ github.event.inputs.tag }}
                  release_name: ${{ github.event.inputs.tag }}
                  draft: true
                  prerelease: false

    add-assets:
        needs: create-release
        name: Add Assets

        strategy:
            matrix:
                target:
                    - artifact: macos
                      artifact_name: |
                          echo "ARTIFACT_NAME=flowsurface-universal-macos.tar.gz" >> "$GITHUB_ENV"
                      asset_type: application/gzip
                    - artifact: windows
                      artifact_name: |
                          echo "ARTIFACT_NAME=flowsurface-x86_64-windows.zip" >> "$GITHUB_ENV"
                      asset_type: application/x-dosexec
                    - artifact: linux
                      artifact_name: |
                          echo "ARTIFACT_NAME=$(bash scripts/package-linux.sh archive_name)" >> "$GITHUB_ENV"
                      asset_type: application/gzip

        runs-on: ubuntu-latest
        env:
            GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}

        steps:
            - uses: actions/checkout@v3

            - name: Download artifact
              uses: actions/download-artifact@v4
              with:
                  name: ${{ matrix.target.artifact }}
                  path: ${{ matrix.target.artifact }}

            - name: Set artifact name
              run: ${{ matrix.target.artifact_name }}

            - name: Upload asset
              uses: actions/upload-release-asset@v1
              env:
                  ARTIFACT_NAME: ${{ env.ARTIFACT_NAME }}
              with:
                  upload_url: ${{ needs.create-release.outputs.upload_url }}
                  asset_path: ./${{ matrix.target.artifact }}/${{ env.ARTIFACT_NAME }}
                  asset_name: ${{ env.ARTIFACT_NAME }}
                  asset_content_type: ${{ matrix.target.asset_type }}
