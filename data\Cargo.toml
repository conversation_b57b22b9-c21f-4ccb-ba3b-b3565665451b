[package]
name = "data"
version = "0.1.0"
edition = "2024"

[dependencies]
serde_json.workspace = true
serde.workspace = true
iced_core.workspace = true
chrono.workspace = true
ordered-float.workspace = true
log.workspace = true
thiserror.workspace = true
regex.workspace = true
palette.workspace = true
enum-map.workspace = true
rustc-hash.workspace = true
dirs-next = "2.0.0"
rodio = { version = "0.20.1", default-features = false, features = [ "wav" ]}
open = "5.3.2"

exchange = { version = "0.1.0", path = "../exchange" }